# DataView 组件权限控制说明

## 概述

DataView.vue 组件已集成完整的权限控制逻辑，参考了 index.vue 的权限实现方式，确保用户只能访问有权限的项目和功能。

## 权限控制层级

### 1. 页面级权限
- 检查用户是否有访问数据视图页面的权限
- 通过 `hasDataViewPermission()` 方法实现
- 无权限时显示 403 错误页面

### 2. 项目组权限
- 控制"查看全部"按钮的显示
- 通过 `checkGroupPermission()` 方法实现
- 支持基于权限码和角色的双重检查

### 3. 项目级权限
- 控制项目卡片的可访问性
- 通过 `checkProjectPermission()` 方法实现
- 无权限的项目显示为禁用状态

## 权限检查方法

### hasDataViewPermission()
```javascript
// 检查用户是否有数据查看权限
hasDataViewPermission() {
  // 1. 检查页面级权限
  const auth = this.$store.state.auth;
  if (auth && auth.pages) {
    const hasPagePermission = auth.pages.some(page => 
      page.id.includes('data-view') || page.id.includes('dataview')
    );
    if (!hasPagePermission) {
      return false;
    }
  }

  // 2. 检查角色权限
  const allowedRoles = ['admin', 'dba', 'leader', 'developer'];
  return allowedRoles.includes(this.role);
}
```

### checkProjectPermission(project)
```javascript
// 检查项目权限
checkProjectPermission(project) {
  // 1. 使用权限系统检查
  if (project.permission_code) {
    return this.$permission.project(project.permission_code);
  }
  
  // 2. 检查角色限制
  if (project.visible_roles && Array.isArray(project.visible_roles)) {
    return project.visible_roles.includes(this.role);
  }

  // 3. 默认允许访问
  return true;
}
```

### checkGroupPermission(group)
```javascript
// 检查项目组权限
checkGroupPermission(group) {
  // 1. 使用权限系统检查
  if (group.permission_code) {
    return this.$permission.projectGroup(group.permission_code);
  }
  
  // 2. 检查角色限制
  if (group.visible_roles && Array.isArray(group.visible_roles)) {
    return group.visible_roles.includes(this.role);
  }

  // 3. 默认允许访问
  return true;
}
```

## 权限过滤逻辑

### filterProjectsByPermission(projects)
- 在数据加载时过滤掉无权限的项目
- 支持嵌套项目的权限过滤
- 确保只显示用户有权限访问的项目

## UI 权限控制

### 1. 权限不足页面
```vue
<div v-if="!hasDataViewPermission()" class="permission-denied">
  <a-result
    status="403"
    title="权限不足"
    sub-title="抱歉，您没有权限访问数据视图页面"
  >
    <template #extra>
      <a-button type="primary" @click="$router.go(-1)">返回</a-button>
    </template>
  </a-result>
</div>
```

### 2. 项目卡片权限控制
```vue
<a-card
  :class="[
    'project-card',
    { 
      active: selectedProject === project.key,
      disabled: !checkProjectPermission(project)
    }
  ]"
  @click="checkProjectPermission(project) ? handleProjectSelect(project) : null"
>
  <a-button
    :disabled="!checkProjectPermission(project)"
    @click.stop="handleProjectJump(project)"
  >
    进入
  </a-button>
</a-card>
```

### 3. 查看全部按钮权限控制
```vue
<a-col v-if="group.children && group.children.length > 0 && checkGroupPermission(group)">
  <a-button @click="handleGroupJump(group)">
    查看全部
  </a-button>
</a-col>
```

## 样式支持

### 禁用状态样式
```less
.project-card {
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f5f5f5;
    
    .project-name {
      color: #bfbfbf;
    }
    
    &:hover {
      box-shadow: none;
      transform: none;
    }
  }
}
```

## 用户体验优化

1. **视觉反馈**: 无权限的项目显示为禁用状态
2. **操作提示**: 点击无权限项目时显示提示信息
3. **优雅降级**: 权限不足时显示友好的错误页面
4. **一致性**: 与系统其他页面的权限控制保持一致

## 测试

运行测试文件验证权限逻辑：
```javascript
// 在浏览器控制台中运行
import('./DataView.test.js').then(test => {
  test.testPermissionLogic();
  test.testPermissionCheck();
});
```

## 注意事项

1. 权限检查依赖于 `$store.state.account.user.role` 和 `$store.state.auth`
2. 项目数据需要包含 `permission_code` 或 `visible_roles` 字段
3. 权限系统需要正确配置 `$permission` 全局方法
4. 建议在后端也进行相应的权限验证，前端权限控制主要用于用户体验优化
