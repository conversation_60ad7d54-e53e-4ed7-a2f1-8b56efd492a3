# DataView.vue HTTP请求更新说明

## 修改概述

将DataView.vue组件中的`loadProjectData`方法从使用`this.$axios`改为使用全局的`Http`实例（来自`@/utils/request`），以确保请求自动携带正确的token信息。

## 修改内容

### 1. 导入Http实例
```javascript
// 修改前
import Index from './index.vue';

// 修改后
import Index from './index.vue';
import { Http } from '@/utils/request';
```

### 2. 更新请求方法
```javascript
// 修改前
const response = await this.$axios({
  url: params.url,
  method: 'get',
  params: params.reqParams
});

// 修改后
const response = await Http({
  url: params.url,
  method: 'get',
  params: params.reqParams
});
```

## 技术优势

### 1. 自动Token处理
使用全局Http实例的请求拦截器会自动处理token：

```javascript
// 请求拦截器自动添加的请求头
config.headers['Authorization'] = Cookie.get(Config.TokenKey) || Cookie.get(Config.LuTokenKey);
config.headers['API-SRC-TOKEN'] = Cookie.get(Config.LuTokenKey);
```

### 2. Token来源
- **Authorization头**: 从`sqlreview_token`或`lu_token` Cookie中获取
- **API-SRC-TOKEN头**: 从`lu_token` Cookie中获取

### 3. 统一错误处理
Http实例包含统一的响应拦截器，能够：
- 自动处理401错误（token失效）
- 统一的错误提示机制
- 自动跳转到登录页面

## Cookie配置

根据`src/utils/config.js`中的配置：
```javascript
'TokenKey': 'sqlreview_token',    // 主要token的cookie键名
'LuTokenKey': 'lu_token',         // 第三方登录token的cookie键名
```

## 请求流程

1. **发起请求**: 调用`Http()`方法
2. **请求拦截**: 自动从Cookie中获取token并添加到请求头
3. **发送请求**: 携带完整的认证信息
4. **响应处理**: 统一的响应拦截器处理结果
5. **错误处理**: 自动识别和处理token相关错误

## 兼容性

- ✅ 与现有的权限系统完全兼容
- ✅ 保持原有的错误处理逻辑
- ✅ 支持Vue 2.7.10版本
- ✅ 向后兼容，不影响其他功能

## 安全性提升

1. **统一认证**: 所有请求都通过统一的认证机制
2. **自动token管理**: 无需手动处理token获取和设置
3. **错误恢复**: token失效时自动跳转登录页面
4. **防止泄露**: token仅在请求头中传输，不暴露在URL中

## 测试建议

1. **正常请求**: 验证数据能正常加载
2. **token失效**: 验证token失效时的跳转逻辑
3. **网络错误**: 验证网络异常时的错误处理
4. **权限检查**: 验证权限控制仍然正常工作

## 注意事项

1. **Cookie依赖**: 确保浏览器支持Cookie且未被禁用
2. **域名配置**: 确保Cookie的域名配置正确
3. **HTTPS**: 生产环境建议使用HTTPS确保token传输安全
4. **过期处理**: 依赖全局拦截器处理token过期情况

---

**修改时间**: 2025-01-26  
**修改状态**: ✅ 已完成  
**影响范围**: DataView.vue组件的数据加载功能  
**向后兼容**: ✅ 完全兼容
