/**
 * DataView 组件权限逻辑测试
 * 测试权限控制功能是否正常工作
 */

// 模拟测试数据
const mockProjectData = [
  {
    key: 'group1',
    title: '项目组1',
    children: [
      {
        key: 'project1',
        title: '项目1',
        permission_code: 'view',
        visible_roles: ['admin', 'dba', 'leader']
      },
      {
        key: 'project2', 
        title: '项目2',
        visible_roles: ['admin', 'dba']
      }
    ]
  },
  {
    key: 'project3',
    title: '独立项目3',
    visible_roles: ['admin', 'dba', 'leader', 'developer']
  }
];

// 模拟不同角色的权限测试
const testPermissionLogic = () => {
  console.log('=== DataView 权限逻辑测试 ===');
  
  // 测试不同角色的权限
  const roles = ['admin', 'dba', 'leader', 'developer'];
  
  roles.forEach(role => {
    console.log(`\n--- 测试角色: ${role} ---`);
    
    // 模拟权限过滤逻辑
    const filteredProjects = mockProjectData.filter(item => {
      if (item.children) {
        // 有子项目的情况
        const filteredChildren = item.children.filter(child => {
          if (child.visible_roles) {
            return child.visible_roles.includes(role);
          }
          return true;
        });
        return filteredChildren.length > 0;
      } else {
        // 独立项目的情况
        if (item.visible_roles) {
          return item.visible_roles.includes(role);
        }
        return true;
      }
    });
    
    console.log(`可访问的项目数量: ${filteredProjects.length}`);
    filteredProjects.forEach(project => {
      console.log(`- ${project.title}`);
      if (project.children) {
        project.children.forEach(child => {
          if (!child.visible_roles || child.visible_roles.includes(role)) {
            console.log(`  - ${child.title}`);
          }
        });
      }
    });
  });
};

// 测试权限检查函数
const testPermissionCheck = () => {
  console.log('\n=== 权限检查函数测试 ===');
  
  const checkProjectPermission = (project, userRole) => {
    if (project.visible_roles && Array.isArray(project.visible_roles)) {
      return project.visible_roles.includes(userRole);
    }
    return true;
  };
  
  const testCases = [
    { project: { key: 'p1', visible_roles: ['admin', 'dba'] }, role: 'admin', expected: true },
    { project: { key: 'p2', visible_roles: ['admin', 'dba'] }, role: 'developer', expected: false },
    { project: { key: 'p3' }, role: 'developer', expected: true }, // 无限制
  ];
  
  testCases.forEach((testCase, index) => {
    const result = checkProjectPermission(testCase.project, testCase.role);
    const status = result === testCase.expected ? '✓' : '✗';
    console.log(`测试 ${index + 1}: ${status} 角色 ${testCase.role} 访问项目 ${testCase.project.key} - 期望: ${testCase.expected}, 实际: ${result}`);
  });
};

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testPermissionLogic,
    testPermissionCheck,
    mockProjectData
  };
} else {
  // 在浏览器环境中运行
  testPermissionLogic();
  testPermissionCheck();
}
